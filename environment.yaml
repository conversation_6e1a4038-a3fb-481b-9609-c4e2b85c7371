name: iti
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - anyio=3.5.0=py38h06a4308_0
  - argon2-cffi=21.3.0=pyhd3eb1b0_0
  - argon2-cffi-bindings=21.2.0=py38h7f8727e_0
  - asttokens=2.0.5=pyhd3eb1b0_0
  - attrs=22.1.0=py38h06a4308_0
  - babel=2.9.1=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - beautifulsoup4=4.11.1=py38h06a4308_0
  - blas=1.0=mkl
  - bleach=4.1.0=pyhd3eb1b0_0
  - brotlipy=0.7.0=py38h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.10.11=h06a4308_0
  - certifi=2022.12.7=py38h06a4308_0
  - cffi=1.15.1=py38h74dc2b5_0
  - cryptography=38.0.1=py38h9ce1e76_0
  - cuda=11.6.2=0
  - cuda-cccl=11.6.55=hf6102b2_0
  - cuda-command-line-tools=11.6.2=0
  - cuda-compiler=11.6.2=0
  - cuda-cudart=11.8.89=0
  - cuda-cudart-dev=11.6.55=h42ad0f4_0
  - cuda-cuobjdump=11.6.124=h2eeebcb_0
  - cuda-cupti=11.8.87=0
  - cuda-cuxxfilt=11.6.124=hecbf4f6_0
  - cuda-driver-dev=11.6.55=0
  - cuda-gdb=12.5.39=0
  - cuda-libraries=11.8.0=0
  - cuda-libraries-dev=11.6.2=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=12.5.39=0
  - cuda-nsight-compute=11.8.0=0
  - cuda-nvcc=11.6.124=hbba6d2d_0
  - cuda-nvdisasm=12.5.39=0
  - cuda-nvml-dev=11.6.55=haa9ef22_0
  - cuda-nvprof=12.5.39=0
  - cuda-nvprune=11.6.124=he22ec0a_0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvrtc-dev=11.6.124=h249d397_0
  - cuda-nvtx=11.8.86=0
  - cuda-nvvp=12.5.39=0
  - cuda-runtime=11.8.0=0
  - cuda-samples=11.6.101=h8efea70_0
  - cuda-sanitizer-api=12.5.39=0
  - cuda-toolkit=11.6.2=0
  - cuda-tools=11.6.2=0
  - cuda-version=12.5=3
  - cuda-visual-tools=11.6.2=0
  - debugpy=1.5.1=py38h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - defusedxml=0.7.1=pyhd3eb1b0_0
  - entrypoints=0.4=py38h06a4308_0
  - executing=0.8.3=pyhd3eb1b0_0
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - gds-tools=********=0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - icu=58.2=he6710b0_3
  - idna=3.4=py38h06a4308_0
  - importlib-metadata=4.11.3=py38h06a4308_0
  - importlib_resources=5.2.0=pyhd3eb1b0_1
  - intel-openmp=2021.4.0=h06a4308_3561
  - ipykernel=6.15.2=py38h06a4308_0
  - ipython=8.6.0=py38h06a4308_0
  - ipython_genutils=0.2.0=pyhd3eb1b0_1
  - jedi=0.18.1=py38h06a4308_1
  - jinja2=3.1.2=py38h06a4308_0
  - jpeg=9e=h7f8727e_0
  - json5=0.9.6=pyhd3eb1b0_0
  - jsonschema=4.16.0=py38h06a4308_0
  - jupyter_client=7.4.7=py38h06a4308_0
  - jupyter_core=4.11.2=py38h06a4308_0
  - jupyter_server=1.18.1=py38h06a4308_0
  - jupyterlab=3.4.4=py38h06a4308_0
  - jupyterlab_pygments=0.1.2=py_0
  - jupyterlab_server=2.15.2=py38h06a4308_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=*********=0
  - libcublas-dev=*********=0
  - libcufft=*********=0
  - libcufft-dev=*********=0
  - libcufile=********=0
  - libcufile-dev=********=0
  - libcurand=*********=0
  - libcurand-dev=*********=0
  - libcusolver=*********=0
  - libcusolver-dev=*********=0
  - libcusparse=*********=0
  - libcusparse-dev=*********=0
  - libdeflate=1.8=h7f8727e_5
  - libffi=3.4.2=h295c915_4
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - libnpp=11.8.0.86=0
  - libnpp-dev=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libnvjpeg-dev=11.9.0.86=0
  - libpng=1.6.37=hbc83047_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.4.0=hecacb30_2
  - libunistring=0.9.10=h27cfd23_0
  - libwebp=1.2.4=h11a3e52_0
  - libwebp-base=1.2.4=h5eee18b_0
  - libxml2=2.9.14=h74e7548_0
  - libxslt=1.1.35=h4e12654_0
  - lxml=4.9.1=py38h1edc446_0
  - lz4-c=1.9.3=h295c915_1
  - markupsafe=2.1.1=py38h7f8727e_0
  - matplotlib-inline=0.1.6=py38h06a4308_0
  - mistune=0.8.4=py38h7b6447c_1000
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.1=py38hd3c417c_0
  - mkl_random=1.2.2=py38h51133e4_0
  - nbclassic=0.4.8=py38h06a4308_0
  - nbclient=0.5.13=py38h06a4308_0
  - nbconvert=6.5.4=py38h06a4308_0
  - nbformat=5.5.0=py38h06a4308_0
  - ncurses=6.3=h5eee18b_3
  - nest-asyncio=1.5.5=py38h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - notebook=6.5.2=py38h06a4308_0
  - notebook-shim=0.2.2=py38h06a4308_0
  - nsight-compute=2022.3.0.22=0
  - numpy=1.23.4=py38h14f4228_0
  - numpy-base=1.23.4=py38h31eccc5_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1s=h7f8727e_0
  - packaging=21.3=pyhd3eb1b0_0
  - pandocfilters=1.5.0=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.2.0=py38hace64e9_1
  - pip=22.2.2=py38h06a4308_0
  - pkgutil-resolve-name=1.3.10=py38h06a4308_0
  - prometheus_client=0.14.1=py38h06a4308_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - psutil=5.9.0=py38h5eee18b_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pyparsing=3.0.9=py38h06a4308_0
  - pyrsistent=0.18.0=py38heee7806_0
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.15=h3fd9d12_0
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python-fastjsonschema=2.16.2=py38h06a4308_0
  - pytorch=2.3.1=py3.8_cuda11.8_cudnn8.7.0_0
  - pytorch-cuda=11.8=h7e8668a_5
  - pytorch-mutex=1.0=cuda
  - pytz=2022.1=py38h06a4308_0
  - pyzmq=23.2.0=py38h6a678d5_0
  - readline=8.2=h5eee18b_0
  - requests=2.28.1=py38h06a4308_0
  - send2trash=1.8.0=pyhd3eb1b0_1
  - sentencepiece=0.1.95=py38hd09550d_0
  - setuptools=65.5.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sniffio=1.2.0=py38h06a4308_1
  - soupsieve=2.3.2.post1=py38h06a4308_0
  - sqlite=3.39.3=h5082296_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - terminado=0.13.1=py38h06a4308_0
  - tinycss2=1.2.1=py38h06a4308_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=2.3.1
  - torchvision=0.18.1
  - tornado=6.2=py38h5eee18b_0
  - traitlets=5.1.1=pyhd3eb1b0_0
  - typing-extensions=4.3.0=py38h06a4308_0
  - typing_extensions=4.3.0=py38h06a4308_0
  - urllib3=1.26.12=py38h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - webencodings=0.5.1=py38_1
  - websocket-client=0.58.0=py38h06a4308_4
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.6=h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zipp=3.8.0=py38h06a4308_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
    - absl-py==1.4.0
    - accelerate==0.21.0
    - aiohttp==3.8.3
    - aiosignal==1.3.1
    - array-record==0.2.0
    - astunparse==1.6.3
    - async-timeout==4.0.2
    - git+https://github.com/davidbau/baukit
    - git+https://github.com/google-research/bleurt
    - cachetools==5.3.0
    - charset-normalizer==2.1.1
    - click==8.1.3
    - colorama==0.4.6
    - contourpy==1.0.7
    - cycler==0.11.0
    - dacite==1.8.1
    - datasets==2.12.0
    - dill==0.3.6
    - dm-tree==0.1.8
    - einops==0.6.1
    - et-xmlfile==1.1.0
    - etils==1.3.0
    - fairscale==0.4.13
    - fancy-einsum==0.0.3
    - filelock==3.8.0
    - fire==0.5.0
    - flatbuffers==23.5.8
    - fonttools==4.39.4
    - frozenlist==1.3.3
    - fsspec==2022.11.0
    - future==0.18.3
    - gast==0.4.0
    - geotorch==0.3.0
    - gin-config==0.5.0
    - google-auth==2.18.0
    - google-auth-oauthlib==1.0.0
    - google-pasta==0.2.0
    - googleapis-common-protos==1.59.0
    - grpcio==1.54.0
    - h5py==3.7.0
    - hickle==5.0.2
    - huggingface-hub==0.16.4
    - ipdb==0.13.9
    - jax==0.4.9
    - joblib==1.2.0
    - keras==2.12.0
    - keyboard==0.13.5
    - kiwisolver==1.4.4
    - libclang==16.0.0
    - llvmlite==0.39.1
    - markdown==3.4.3
    - matplotlib==3.7.1
    - mesh-tensorflow==0.1.21
    - ml-dtypes==0.1.0
    - multidict==6.0.3
    - multiprocess==0.70.14
    - nltk==3.8.1
    - numba==0.56.4
    - oauthlib==3.2.2
    - openai==0.25.0
    - openpyxl==3.0.10
    - opt-einsum==3.3.0
    - pandas==2.0.1
    - pandas-stubs==1.5.1.221024
    - parallelformers==1.2.7
    - plotly==5.14.1
    - portalocker==2.7.0
    - promise==2.3
    - protobuf==4.23.0
    - pyarrow==10.0.1
    - pyasn1==0.5.0
    - pyasn1-modules==0.3.0
    - pynndescent==0.5.8
    - pyyaml==6.0
    - regex==2022.10.31
    - requests-oauthlib==1.3.1
    - responses==0.18.0
    - rouge-score==0.1.2
    - rsa==4.9
    - sacrebleu==2.3.1
    - sacremoses==0.0.53
    - safetensors==0.3.1
    - scikit-learn==1.2.2
    - scipy==1.10.1
    - seaborn==0.12.2
    - t5==0.7.1
    - tabulate==0.9.0
    - tenacity==8.2.2
    - tensorboard==2.12.3
    - tensorboard-data-server==0.7.0
    - tensorflow==2.12.0
    - tensorflow-datasets==4.9.2
    - tensorflow-estimator==2.12.0
    - tensorflow-hub==0.13.0
    - tensorflow-io-gcs-filesystem==0.32.0
    - tensorflow-metadata==1.13.1
    - tensorflow-text==2.12.1
    - termcolor==2.2.0
    - tf-slim==1.1.0
    - tfds-nightly==4.9.2.dev202305230044
    - threadpoolctl==3.1.0
    - tokenizers==0.15.0
    - toml==0.10.2
    - tqdm==4.64.1
    - transformers==4.35.2
    - git+https://github.com/sylinrl/TruthfulQA
    - types-pytz==2022.6.0.1
    - tzdata==2023.3
    - umap-learn==0.5.3
    - werkzeug==2.3.4
    - wrapt==1.14.1
    - xxhash==3.1.0
    - yarl==1.8.2
  
