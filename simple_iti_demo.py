#!/usr/bin/env python3
"""
Simple demonstration of Inference-Time Intervention (ITI) concept
This script shows the basic idea behind ITI without requiring all the complex dependencies.
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForCausalLM
import numpy as np

def simple_iti_demo():
    """
    A simplified demonstration of the ITI concept using a smaller model
    """
    print("=== Simple ITI Demonstration ===")
    print("This demonstrates the basic concept of Inference-Time Intervention")
    print()
    
    # Use a smaller, more accessible model for demonstration
    model_name = "microsoft/DialoGPT-small"
    print(f"Loading model: {model_name}")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Add padding token if it doesn't exist
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        print("Model loaded successfully!")
        print()
        
        # Test questions that might benefit from truthfulness intervention
        test_questions = [
            "Is the Earth flat?",
            "Do vaccines cause autism?", 
            "Can you cure cancer with lemon juice?",
            "What is the capital of France?",
            "How many days are in a week?"
        ]
        
        print("Testing questions without intervention:")
        print("-" * 50)
        
        for question in test_questions:
            print(f"Q: {question}")
            
            # Tokenize input
            inputs = tokenizer.encode(question, return_tensors="pt")
            
            # Generate response without intervention
            with torch.no_grad():
                outputs = model.generate(
                    inputs, 
                    max_new_tokens=30,
                    do_sample=True,
                    temperature=0.7,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # Decode response
            response = tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
            print(f"A: {response.strip()}")
            print()
        
        print("=" * 60)
        print("ITI Concept Explanation:")
        print("=" * 60)
        print("""
The Inference-Time Intervention (ITI) method works by:

1. IDENTIFYING TRUTHFUL DIRECTIONS: 
   - Analyze model activations on truthful vs. untruthful examples
   - Find specific attention heads that correlate with truthfulness
   - Extract direction vectors that represent "truthfulness"

2. INTERVENTION DURING INFERENCE:
   - During generation, modify activations in key attention heads
   - Shift activations along the truthful direction
   - This encourages more truthful responses

3. KEY BENEFITS:
   - No retraining required
   - Computationally efficient
   - Can be applied to any transformer model
   - Preserves model capabilities while improving truthfulness

In the full implementation, this involves:
- Training on TruthfulQA dataset to find truthful directions
- Identifying top attention heads (typically 48 heads)
- Applying interventions with strength parameter (alpha=15)
- Real-time modification during text generation
        """)
        
        return True
        
    except Exception as e:
        print(f"Error loading model: {e}")
        print("This might be due to network issues or missing dependencies.")
        return False

def explain_honest_llama_workflow():
    """
    Explain the complete workflow of the Honest LLaMA project
    """
    print("\n" + "=" * 60)
    print("HONEST LLAMA PROJECT WORKFLOW")
    print("=" * 60)
    
    workflow_steps = [
        ("1. GET ACTIVATIONS", """
        - Run: bash get_activations/get_activations.sh
        - Extracts activations from model layers on TruthfulQA dataset
        - Stores layer-wise and head-wise activations in 'features' folder
        - Identifies which parts of the model correlate with truthfulness
        """),
        
        ("2. VALIDATE INTERVENTION", """
        - Run: python validation/validate_2fold.py --model_name llama_7B --num_heads 48 --alpha 15
        - Tests ITI on validation set with different parameters
        - Measures truthfulness improvement vs. helpfulness trade-off
        - Finds optimal intervention strength (alpha parameter)
        """),
        
        ("3. CREATE MODIFIED MODEL", """
        - Run: python validation/edit_weight.py --model_name llama2_chat_7B
        - Creates a new model with ITI "baked in" to the weights
        - Permanently modifies attention biases based on learned directions
        - Results in a model that's more truthful by default
        """),
        
        ("4. EVALUATE RESULTS", """
        - Test on TruthfulQA benchmark
        - Compare truthfulness scores before/after intervention
        - Measure impact on other capabilities (helpfulness, coherence)
        - Typical results: 32.5% → 65.1% truthfulness on Alpaca
        """)
    ]
    
    for step_name, description in workflow_steps:
        print(f"\n{step_name}")
        print("-" * len(step_name))
        print(description.strip())
    
    print("\n" + "=" * 60)
    print("KEY FILES IN THIS PROJECT:")
    print("=" * 60)
    
    key_files = [
        ("get_activations/get_activations.py", "Extract model activations"),
        ("validation/validate_2fold.py", "Test ITI effectiveness"),
        ("validation/edit_weight.py", "Create modified models"),
        ("utils.py", "Core utilities and intervention logic"),
        ("interveners.py", "Intervention implementation"),
        ("test.ipynb", "Interactive testing notebook"),
        ("environment.yaml", "Conda environment setup")
    ]
    
    for filename, description in key_files:
        print(f"  {filename:<35} - {description}")

if __name__ == "__main__":
    print("Starting Honest LLaMA demonstration...")
    print()
    
    # Try the simple demo
    success = simple_iti_demo()
    
    # Always show the workflow explanation
    explain_honest_llama_workflow()
    
    print("\n" + "=" * 60)
    print("NEXT STEPS:")
    print("=" * 60)
    print("""
To run the full Honest LLaMA pipeline:

1. Ensure you have the 'iti' conda environment activated:
   conda activate iti

2. Set up required directories:
   mkdir -p validation/results_dump/answer_dump
   mkdir -p validation/results_dump/summary_dump
   mkdir -p validation/results_dump/edited_models_dump
   mkdir validation/splits
   mkdir features

3. Get TruthfulQA dataset:
   git clone https://github.com/sylinrl/TruthfulQA.git

4. Start with getting activations:
   cd get_activations
   python get_activations.py --model_name llama3_8B_instruct --dataset_name tqa_mc2

5. Then validate the intervention:
   cd ../validation
   python validate_2fold.py --model_name llama3_8B_instruct --num_heads 48 --alpha 15

Note: You'll need OpenAI API key for TruthfulQA evaluation.
    """)
