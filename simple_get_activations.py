#!/usr/bin/env python3
"""
Simplified version of get_activations.py for demonstration
This shows how to extract activations from a model without all the complex dependencies
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForCausalLM
import numpy as np
import pickle
import os
from tqdm import tqdm

def get_simple_activations():
    """
    Extract activations from a smaller model for demonstration
    """
    print("=== Simple Activation Extraction Demo ===")
    print("This demonstrates how ITI extracts model activations")
    print()
    
    # Use a smaller model for demonstration
    model_name = "gpt2"
    print(f"Loading model: {model_name}")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        # Add padding token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        print("Model loaded successfully!")
        print(f"Model has {model.config.n_layer} layers")
        print(f"Each layer has {model.config.n_head} attention heads")
        print()
        
        # Sample truthful and untruthful statements
        truthful_statements = [
            "The Earth is round.",
            "Water boils at 100 degrees Celsius.",
            "Paris is the capital of France.",
            "There are 7 days in a week.",
            "The sun is a star."
        ]
        
        untruthful_statements = [
            "The Earth is flat.",
            "Water boils at 50 degrees Celsius.", 
            "London is the capital of France.",
            "There are 10 days in a week.",
            "The sun is a planet."
        ]
        
        print("Extracting activations from truthful statements...")
        truthful_activations = []
        
        for statement in tqdm(truthful_statements):
            inputs = tokenizer(statement, return_tensors="pt", padding=True)
            
            with torch.no_grad():
                # Get model outputs with hidden states
                outputs = model(**inputs, output_hidden_states=True)
                hidden_states = outputs.hidden_states
                
                # Extract activations from the last token of each layer
                layer_activations = []
                for layer_idx, layer_hidden in enumerate(hidden_states[1:]):  # Skip embedding layer
                    # Get last token activation
                    last_token_activation = layer_hidden[0, -1, :].cpu().numpy()
                    layer_activations.append(last_token_activation)
                
                truthful_activations.append(layer_activations)
        
        print("Extracting activations from untruthful statements...")
        untruthful_activations = []
        
        for statement in tqdm(untruthful_statements):
            inputs = tokenizer(statement, return_tensors="pt", padding=True)
            
            with torch.no_grad():
                outputs = model(**inputs, output_hidden_states=True)
                hidden_states = outputs.hidden_states
                
                layer_activations = []
                for layer_idx, layer_hidden in enumerate(hidden_states[1:]):
                    last_token_activation = layer_hidden[0, -1, :].cpu().numpy()
                    layer_activations.append(last_token_activation)
                
                untruthful_activations.append(layer_activations)
        
        # Convert to numpy arrays
        truthful_activations = np.array(truthful_activations)
        untruthful_activations = np.array(untruthful_activations)
        
        print(f"Truthful activations shape: {truthful_activations.shape}")
        print(f"Untruthful activations shape: {untruthful_activations.shape}")
        print()
        
        # Compute difference directions (simplified version of ITI)
        print("Computing truthfulness directions...")
        truthfulness_directions = []
        
        for layer_idx in range(truthful_activations.shape[1]):
            # Get activations for this layer
            truthful_layer = truthful_activations[:, layer_idx, :]
            untruthful_layer = untruthful_activations[:, layer_idx, :]
            
            # Compute mean difference (simplified direction)
            mean_truthful = np.mean(truthful_layer, axis=0)
            mean_untruthful = np.mean(untruthful_layer, axis=0)
            direction = mean_truthful - mean_untruthful
            
            # Normalize direction
            direction = direction / (np.linalg.norm(direction) + 1e-8)
            truthfulness_directions.append(direction)
        
        truthfulness_directions = np.array(truthfulness_directions)
        print(f"Truthfulness directions shape: {truthfulness_directions.shape}")
        
        # Save results
        os.makedirs("features", exist_ok=True)
        
        results = {
            'truthful_activations': truthful_activations,
            'untruthful_activations': untruthful_activations,
            'truthfulness_directions': truthfulness_directions,
            'truthful_statements': truthful_statements,
            'untruthful_statements': untruthful_statements,
            'model_name': model_name,
            'num_layers': model.config.n_layer,
            'num_heads': model.config.n_head,
            'hidden_size': model.config.n_embd
        }
        
        with open('features/simple_activations.pkl', 'wb') as f:
            pickle.dump(results, f)
        
        print("Results saved to features/simple_activations.pkl")
        print()
        
        # Analyze the directions
        print("=== Analysis of Truthfulness Directions ===")
        
        # Compute magnitude of directions per layer
        direction_magnitudes = np.linalg.norm(truthfulness_directions, axis=1)
        
        print("Direction magnitudes by layer:")
        for i, mag in enumerate(direction_magnitudes):
            print(f"  Layer {i:2d}: {mag:.4f}")
        
        # Find layers with strongest truthfulness signal
        top_layers = np.argsort(direction_magnitudes)[-3:][::-1]
        print(f"\nTop 3 layers with strongest truthfulness signal: {top_layers}")
        
        print("\n" + "=" * 60)
        print("WHAT THIS DEMONSTRATES:")
        print("=" * 60)
        print("""
1. ACTIVATION EXTRACTION:
   - We fed truthful and untruthful statements to the model
   - Extracted hidden state activations from each layer
   - Focused on the last token (where the model "decides" truthfulness)

2. DIRECTION COMPUTATION:
   - Computed the difference between truthful and untruthful activations
   - This gives us a "truthfulness direction" for each layer
   - Layers with larger direction magnitudes are more important for truthfulness

3. INTERVENTION POTENTIAL:
   - In the full ITI method, these directions would be used during inference
   - By shifting activations along these directions, we can encourage truthfulness
   - The strength of intervention (alpha parameter) controls how much to shift

4. NEXT STEPS:
   - In the real implementation, this uses attention head activations
   - More sophisticated methods identify the most important heads
   - The directions are refined using larger datasets like TruthfulQA
        """)
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = get_simple_activations()
    
    if success:
        print("\n" + "=" * 60)
        print("SUCCESS! Activation extraction completed.")
        print("=" * 60)
        print("""
This simplified demo shows the core concept behind ITI activation extraction.

To run the full version with LLaMA models:
1. Ensure you have access to LLaMA models (requires HuggingFace authentication)
2. Run: python get_activations/get_activations.py --model_name llama3_8B_instruct
3. This will extract activations using the TruthfulQA dataset
4. Results will be saved in the features/ directory

The full version includes:
- More sophisticated attention head analysis
- Larger datasets for better direction estimation  
- Support for various model architectures
- Optimized processing for large models
        """)
    else:
        print("\nDemo failed. Check error messages above.")
